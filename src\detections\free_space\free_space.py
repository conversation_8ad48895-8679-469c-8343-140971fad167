import time
from typing import NamedTuple

import cv2 as cv
import matplotlib.pyplot as plt
import numpy as np

from src.detections.free_space.lir_basis import largest_interior_rectangle
from src.detections.free_space.model.unet_infer import infer
from src.utils.max_pool import crop_image_to_multiple, min_pool_resize


class EmptyRectArea(NamedTuple):
    u: int
    v: int
    w: int
    h: int

    r: int = 0
    theta: int = 0
    z: int = 0


def sort_to_preference(candidates: list[EmptyRectArea], max_candidates: int = 3) -> list:
    """
    Sorts the candidates to prefer the lowest possible position, next prefer larger sizes.
    """
    candidates = sorted(
        candidates, key=lambda candidate: candidate.v + candidate.h, reverse=True
    )  # Prefer lowest possible position
    candidates = candidates[:max_candidates]
    candidates = sorted(
        candidates, key=lambda candidate: candidate.w * candidate.h, reverse=True
    )
    return candidates


def generate_candidates(image: np.ndarray, max_candidates: int = 3) -> list[EmptyRectArea]:
    image = cv.threshold(image, 254, 255, cv.THRESH_BINARY_INV)[1]
    image = cv.dilate(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=2)
    image = cv.erode(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=200)
    image = cv.dilate(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=125)
    contours, _ = cv.findContours(image, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
    candidates = []
    for contour in contours:
        x, y, w, h = cv.boundingRect(contour)
        x = max(0, x)
        y = max(0, y)
        w = min(image.shape[1], w)
        h = min(image.shape[0], h)
        candidates.append(EmptyRectArea(x, y, w, h))
    candidates = sort_to_preference(candidates, max_candidates=max_candidates)
    return candidates


def preprocess_profile_image(profile: np.ndarray) -> np.ndarray:
    """
    Code currently from Nippon - Label check
    """
    profile = np.array(profile, dtype=np.float64)
    profile = cv.rotate(profile, cv.ROTATE_90_COUNTERCLOCKWISE)
    profile = cv.flip(profile, 1)

    threshval = profile.min() / 1e2
    profile[profile <= threshval] = 0

    profile = -profile / 1e6

    blurred = cv.blur(profile, (34, 92))
    retval = profile - blurred

    retval[retval < 1e-4] = 1e-3
    retval[retval > 5e-3] = 1e-2

    retval = cv.normalize(retval, retval, 0, 255, cv.NORM_MINMAX, cv.CV_8UC1)
    retval = cv.morphologyEx(
        retval, cv.MORPH_CLOSE, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=3
    )
    return retval


def main():
    file = 'data/OCR/20250415/pointclouds/20241203_132659942244.npy'
    data = np.load(file)
    image = preprocess_profile_image(data)
    # image = cv.imread(
    #     'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.png',
    #     cv.IMREAD_GRAYSCALE,
    # )
    image = image[1242:3242, :]
    SEGMENT_SCALE = 4
    RECT_SEARCH_SCALE = 8
    image = crop_image_to_multiple(image, SEGMENT_SCALE * RECT_SEARCH_SCALE)
    image = cv.resize(
        image,
        (0, 0),
        fx=1 / SEGMENT_SCALE,
        fy=1 / SEGMENT_SCALE,
        interpolation=cv.INTER_NEAREST,
    )
    # segmentation = infer(image)  # Warmup: uncomment to see real inference time
    start = time.perf_counter()
    segmentation = infer(image.astype(np.uint8))
    end = time.perf_counter()
    print(f'Segmentation time: {end - start:.6f} seconds')
    segmentation = cv.erode(
        segmentation, cv.getStructuringElement(cv.MORPH_RECT, (3, 3)), iterations=2
    )
    segmentation = cv.dilate(
        segmentation,
        cv.getStructuringElement(cv.MORPH_RECT, (3, 3)),
        iterations=4,  # a little margin
    )
    binary_mask = segmentation < 0.5
    # rect = find_largest_rectangle(binary_mask)
    binary_mask_downscaled = min_pool_resize(binary_mask, 1 / RECT_SEARCH_SCALE).astype(np.bool_)
    start = time.perf_counter()
    rect = largest_interior_rectangle(
        binary_mask_downscaled,
        min_width=int(100 / RECT_SEARCH_SCALE),
        min_height=int(100 / RECT_SEARCH_SCALE),
        min_area=int(10000 / (RECT_SEARCH_SCALE**2)),
    )
    end = time.perf_counter()
    print(f'Rect find time: {end - start:.6f} seconds')
    rect = rect * RECT_SEARCH_SCALE
    print(rect)

    show = cv.cvtColor(image, cv.COLOR_GRAY2RGB)
    cv.rectangle(
        show,
        (rect[0], rect[1]),
        (rect[0] + rect[2] - 1, rect[1] + rect[3] - 1),
        (0, 255, 0),
        10,
    )
    fig, ax = plt.subplots(2, 1, sharex=True, sharey=True, figsize=(20, 8))
    ax[0].imshow(show, cmap='gray')
    ax[0].set_title('Image and largest rectangle')
    ax[1].imshow(1 - binary_mask, cmap='viridis')
    ax[1].set_title('Segmentation')
    plt.show()

    return rect


if __name__ == '__main__':
    main()
